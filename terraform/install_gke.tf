resource "google_container_cluster" "gke_cluster" {
  name     = var.cluster_name
  location = var.cluster_location
  project  = var.project_id
  

  network    = "projects/${var.project_id}/global/networks/${var.network_name}"
  subnetwork = "projects/${var.project_id}/regions/${var.region}/subnetworks/${var.subnetwork_name}"

  release_channel {
    channel = var.release_channel
  }

  remove_default_node_pool = true
  initial_node_count       = var.initial_node_count

  enable_shielded_nodes = var.enable_shielded_nodes
  deletion_protection   = var.deletion_protection

  ip_allocation_policy {
    cluster_secondary_range_name  = var.pods_secondary_range_name
    services_secondary_range_name = var.services_secondary_range_name
  }

  master_authorized_networks_config {
    dynamic "cidr_blocks" {
      for_each = var.authorized_networks
      content {
        cidr_block   = cidr_blocks.value.cidr_block
        display_name = cidr_blocks.value.display_name
      }
    }
    gcp_public_cidrs_access_enabled = var.gcp_public_cidrs_access_enabled
  }

  logging_config {
    enable_components = var.logging_components
  }

  monitoring_config {
    enable_components = var.monitoring_components
  }

  addons_config {
    gce_persistent_disk_csi_driver_config {
      enabled = var.enable_gce_persistent_disk_csi_driver
    }

    horizontal_pod_autoscaling {
      disabled = var.disable_horizontal_pod_autoscaling
    }

    http_load_balancing {
      disabled = var.disable_http_load_balancing
    }

    network_policy_config {
      disabled = var.disable_network_policy
    }

    gcs_fuse_csi_driver_config {
      enabled = var.enable_gcs_fuse_csi_driver
    }

    dns_cache_config {
      enabled = var.enable_dns_cache
    }
  }

  datapath_provider = var.datapath_provider
}
