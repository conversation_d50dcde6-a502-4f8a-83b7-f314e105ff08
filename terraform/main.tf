provider "google" {
  project     = "lif-prod"
  region      = "asia-southeast2"
}

# Create a VPC
resource "google_compute_network" "lif_vpc_prod" {
  name                    = "lif-vpc-prod"
  auto_create_subnetworks = false
}

# Create a public subnet
resource "google_compute_subnetwork" "public_subnet" {
  name          = "public-subnet"
  region        = "asia-southeast2"
  network       = google_compute_network.lif_vpc_prod.name
  ip_cidr_range = "********/24"
}

# Create a private subnet
resource "google_compute_subnetwork" "private_subnet" {
  name          = "private-subnet"
  region        = "asia-southeast2"
  network       = google_compute_network.lif_vpc_prod.name
  ip_cidr_range = "********/24"
}

# create ip public static
resource "google_compute_address" "static_ip" {
  name   = "bastion-static-ip"
  region = "asia-southeast2"  # Update the region as per your requirement
}

# Create a bastion server in the public subnet
resource "google_compute_instance" "bastion" {
  name         = "bastion-server"
  machine_type = "e2-highcpu-4"
  zone         = "asia-southeast2-a"
  tags         = ["allow-ssh"]

  boot_disk {
    initialize_params {
      image = "ubuntu-os-cloud/ubuntu-2004-lts"
    }
  }

  network_interface {
    network = google_compute_network.lif_vpc_prod.name
    subnetwork = google_compute_subnetwork.public_subnet.name
    access_config {
      nat_ip = google_compute_address.static_ip.address
    }
  }
}

# Define the router
resource "google_compute_router" "nat_router" {
  name    = "nat-router"
  region  = "asia-southeast2"
  network = google_compute_network.lif_vpc_prod.name
}

# Define the NAT configuration with automatic IP allocation
resource "google_compute_router_nat" "nat_config" {
  name                               = "nat-config"
  router                             = google_compute_router.nat_router.name
  region                             = "asia-southeast2"
  nat_ip_allocate_option             = "AUTO_ONLY"
  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"
}


# Define the route for the private subnet
resource "google_compute_route" "private_subnet_route" {
  name            = "private-subnet-route"
  network         = google_compute_network.lif_vpc_prod.name
  dest_range      = "0.0.0.0/0"
  next_hop_gateway = google_compute_router.nat_router.name
}
