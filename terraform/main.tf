provider "google" {
  project     = "lif-prod"
  region      = "asia-southeast2"
}

# Create a VPC
resource "google_compute_network" "lif_vpc_prod" {
  name                    = "lif-vpc-prod"
  auto_create_subnetworks = false
}

# Create a public subnet
resource "google_compute_subnetwork" "public_subnet" {
  name          = "public-subnet"
  region        = "asia-southeast2"
  network       = google_compute_network.lif_vpc_prod.name
  ip_cidr_range = "********/24"
}

# Create a private subnet
resource "google_compute_subnetwork" "private_subnet" {
  name          = "private-subnet"
  region        = "asia-southeast2"
  network       = google_compute_network.lif_vpc_prod.name
  ip_cidr_range = "********/24"
}

# create ip public static
resource "google_compute_address" "static_ip" {
  name   = "bastion-static-ip"
  region = "asia-southeast2"  # Update the region as per your requirement
}

# Create a bastion server in the public subnet
resource "google_compute_instance" "bastion" {
  name         = "bastion-server"
  machine_type = "e2-highcpu-4"
  zone         = "asia-southeast2-a"
  tags         = ["allow-ssh"]

  boot_disk {
    initialize_params {
      image = "ubuntu-os-cloud/ubuntu-2004-lts"
    }
  }

  # Attached disk (preserve existing disk-1)
  attached_disk {
    source      = "https://www.googleapis.com/compute/v1/projects/lif-prod/zones/asia-southeast2-a/disks/disk-1"
    device_name = "disk-1"
    mode        = "READ_WRITE"
  }

  network_interface {
    network = google_compute_network.lif_vpc_prod.name
    subnetwork = google_compute_subnetwork.public_subnet.name
    access_config {
      nat_ip = google_compute_address.static_ip.address
    }
  }

  # Service account configuration
  service_account {
    email  = "<EMAIL>"
    scopes = [
      "https://www.googleapis.com/auth/cloud-platform",
      "https://www.googleapis.com/auth/compute"
    ]
  }

  # Metadata including SSH keys and OS config
  metadata = {
    enable-osconfig = "TRUE"
    ssh-keys = <<-EOT
      roy_a:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDFuHwIN25DCiqOUdbY188PFnix16IWQBwib+jBIEBz5IB2NYVYqkVGzh4O8lCy1HLXwHavy27gPuCz4gbyUeDFKzyuh0ufCxSXVNz/r5GqZLqPFOf/B7txS3y+ojpZ0StV0C81iV/WS4q3qpM+tOzd12Dl3E16a1aYa9o6G+FVyoaFlMJ1Hngb8C2pSwkW+esrK05Y7NeSu//Yb9idIqSlhXbmgJW5eIyYA46Zj7UFu/mZB6rzGOWW9Lms6gPirT0mRj5cyxeN6XzHQo64Rn3ArQtr0qJxVB96imX2PDpTiBsIX84dx6Udt+wrikeiGO7wE5Ew6F6VeiZv6ZVYu+WL roy_a
      roy_a:ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBOm5Q2uo0ZG7Zmx5nUPOcjtUbBh6ko+jNmiQxXfn5Xeur8I1X/f5P/QGxSP5W/pdYPnPF1kHoKtU2bP928gAa7s= google-ssh {"userName":"<EMAIL>","expireOn":"2025-04-04T03:41:33+0000"}
      roy_a:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAGLq6eMpRkIwHDA9hdCGuW6tDJmiN90TzmzymKJXVOAFQeUxy59iJBRW/5Ec1UyV9PvpSvvzSs50oLuAFnA7jFmD5xgskCFQV73VAia2+4xedXucnsg0gmeC3XzSTEfK0T+Fp+pLfehyl7g698iGTk74R7heKJE202tgLQ45QJRh7JsTdGl6PC8FYybVlENeUXxH8vByBHJA/AlaJF1HXSbqxldIUAgIsUa14yOdBDUQJA0d+RgBvPlTzPF8BdkC/1y83ve1n3XIKvV7KQH7tt4JH44YkbRqwrz/KQ9IVod3EBda3+toFwnqJ+ee572nnl1CIQ2CBEpYTOucYjTUJ28= google-ssh {"userName":"<EMAIL>","expireOn":"2025-04-04T03:41:37+0000"}
    EOT
  }
}

# Define the router
resource "google_compute_router" "nat_router" {
  name    = "nat-router"
  region  = "asia-southeast2"
  network = google_compute_network.lif_vpc_prod.name
}

# Define the NAT configuration with automatic IP allocation
resource "google_compute_router_nat" "nat_config" {
  name                               = "nat-config"
  router                             = google_compute_router.nat_router.name
  region                             = "asia-southeast2"
  nat_ip_allocate_option             = "AUTO_ONLY"
  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"
}


# Define the route for the private subnet
resource "google_compute_route" "private_subnet_route" {
  name            = "private-subnet-route"
  network         = google_compute_network.lif_vpc_prod.name
  dest_range      = "0.0.0.0/0"
  next_hop_gateway = google_compute_router.nat_router.name
}
