# Custom Image Resource
resource "google_compute_image" "custom_image" {
  name         = "base-images-lif-prod-2023-ubuntu22-04-25"
  description  = "Custom image created from my-custom-snapshot"
  source_image = "https://www.googleapis.com/compute/v1/projects/lif-prod/global/images/base-images-lif-prod-2023-ubuntu22-04"
}

# Reusable locals
locals {
  zone         = "asia-southeast2-a"
  network      = google_compute_network.lif_vpc_prod.name
  subnetwork   = google_compute_subnetwork.private_subnet.name
  image_link   = google_compute_image.custom_image.self_link
}

# Redis Server
resource "google_compute_instance" "redis_server" {
  name         = "lif-prod-redis"
  machine_type = "e2-custom-2-4096"
  zone         = local.zone
  tags         = ["allow-ssh", "allow-icmp"]
  allow_stopping_for_update = true

  boot_disk {
    initialize_params {
      image = local.image_link
      # WARNING: Changing this will recreate the instance
      size  = 50
    }
  }

  network_interface {
    network    = local.network
    subnetwork = local.subnetwork
    network_ip = "********"
  }

  # Service account configuration
  service_account {
    email  = "<EMAIL>"
    scopes = [
      "https://www.googleapis.com/auth/cloud-platform",
      "https://www.googleapis.com/auth/compute"
    ]
  }

  # Metadata including SSH keys and OS config
  metadata = {
    enable-osconfig = "TRUE"
    ssh-keys = <<-EOT
      lookman_af:ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBAaOJWKiqk2fWwDvbsLhqPQghURSbr7ht/rxMPn2LgTP8nwkHAzbtHgC1AqdDVl8F1ozjwtnV0QSYyHJABH0fTM= google-ssh {"userName":"<EMAIL>","expireOn":"2025-02-28T09:28:11+0000"}
      lookman_af:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCNc7diRs6guXpyYdPEpuVzWvoWPMAsIdA4gkVErEqOaWQAywPBW4kVKBE5iySFjFbHV3SzNSq+aIb5UYs+s/W5CbOxieGhC/QVWt4iIwifvspTef7dV0hUpf+YaM/PcYaG9tTQOVeLgbs5MOy+nzAppTauVNbuNn6cLzGT6aL0yU+9IPXm7kgqSfonplJFW/e1CtAScpoew/jUaIK30fqMppho8HMRHuCtdf1mrPmXz/y/som2MqL/rYsHr08jhl10khGQHYSFVqkAwYHBQCCtW8BPE8RzQLmCQxrn+9saubaXSRZY85uvWo5B785Zc54dSBKXJu9e13lbizzbgozn google-ssh {"userName":"<EMAIL>","expireOn":"2025-02-28T09:28:14+0000"}
    EOT
  }
}

# RabbitMQ Server
resource "google_compute_instance" "rabbitmq_server" {
  name         = "lif-prod-rabbitmq"
  machine_type = "e2-custom-2-4096"
  zone         = local.zone
  tags         = ["allow-ssh", "allow-icmp"]
  allow_stopping_for_update = true

  boot_disk {
    initialize_params {
      image = local.image_link
      size  = 20
    }
  }

  network_interface {
    network    = local.network
    subnetwork = local.subnetwork
    network_ip = "********"
  }

  # Service account configuration
  service_account {
    email  = "<EMAIL>"
    scopes = [
      "https://www.googleapis.com/auth/cloud-platform",
      "https://www.googleapis.com/auth/compute"
    ]
  }

  # Metadata including SSH keys
  metadata = {
    ssh-keys = <<-EOT
      lookman_af:ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBBxN+YGoy+WZ6AIMIiyKo9aty9ZX/7qvyc0wHSM0pQ19kGdQ73FZWncUL6xMMr8px/ts7DOmcrJ1CvPGVUf/bfA= google-ssh {"userName":"<EMAIL>","expireOn":"2025-02-11T14:41:59+0000"}
      lookman_af:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAH+My9HIb3Mc7tJGl2sTf7XJftz/zxdlnM2HKjgaPQ2XSCJxsKniVrY6AKqcQ4XdWlyEtzWdiQcoLEXiSZTdPU5iDe3v3QqDU8E/4+s+HrWS+qaJOfKvcwWpJTOESm+6NaDLj4NU+G4v13g933kvkA3zlCtZU8d+UQVIbmKgrjA1MG7i2PmKFpUPNS59ehXkguBAVNNRqjpO53Omp5a9TsRBkB0Asz7ummzjTDIa+3QxwfObWNBbDv+0eHJRNxeeWCvlF0Qy/1nQozIHuPv/OKmXw6x6+gKEidLrkMOfDmalQxlFROUAja3K0B80794qXezX3w5p1vVmFroRAaS6LOM= google-ssh {"userName":"<EMAIL>","expireOn":"2025-02-11T14:42:13+0000"}
    EOT
  }
}

# MongoDB Server
resource "google_compute_instance" "mongodb_server" {
  name         = "lif-prod-mongodb"
  machine_type = "e2-custom-2-4096"
  zone         = local.zone
  tags         = ["allow-ssh", "allow-icmp"]
  allow_stopping_for_update = true

  boot_disk {
    initialize_params {
      image = local.image_link
      size  = 100
    }
  }

  # Attached disk (preserve existing disk-2)
  attached_disk {
    source      = "https://www.googleapis.com/compute/v1/projects/lif-prod/regions/asia-southeast2/disks/disk-2"
    device_name = "disk-2"
    mode        = "READ_WRITE"
  }

  network_interface {
    network    = local.network
    subnetwork = local.subnetwork
    network_ip = "********"
  }

  # Service account configuration
  service_account {
    email  = "<EMAIL>"
    scopes = [
      "https://www.googleapis.com/auth/cloud-platform",
      "https://www.googleapis.com/auth/compute"
    ]
  }

  # Metadata including SSH keys
  metadata = {
    ssh-keys = <<-EOT
      lookman_af:ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBJ/kfLrT/ct+4nTZyvYJzYOBN2fVCvCeuotQb1cV18J9CyiOwGBKSmeJcOQ5VNXOYV61MKO13sKj8FhjK+1vGEY= google-ssh {"userName":"<EMAIL>","expireOn":"2025-02-11T14:21:39+0000"}
      lookman_af:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCqx/6vc/EY03oB5HyCNBAi4RNWrmmnCkdyySnS79arzwEIF8bt78GddaJKZknZhkEzbKNEKBGpbbr2kkL3UdEJ1tHdJs3uAuG+3nqFqeo5qTsTrmznNncsxyO7nkfvwrEiJ3TuMfRby01vexDhT5Syzupv33NT6qIZiNerKAPk9fZ/JMiNLBZlerypFHassCt9SPTgHd5mFinFvWfBvm8+TfhPPJENyO9Q0VuV6Y4YYiPEKfOMfr2F15JWT4Do+tx78KHpEDzgPDlmwbhpvBmVUZ/KuStN6im5qmTkIMtK6tckuStzttNJtLXBAJEQ+wgKSoqEpcONOuDtvC1XbQat google-ssh {"userName":"<EMAIL>","expireOn":"2025-02-11T14:21:44+0000"}
    EOT
  }
}

# PostgreSQL Server
resource "google_compute_instance" "postgres_server" {
  name         = "lif-prod-postgres"
  machine_type = "e2-custom-4-4096"
  zone         = local.zone
  tags         = ["allow-ssh", "allow-icmp"]
  allow_stopping_for_update = true

  boot_disk {
    initialize_params {
      image = local.image_link
      size  = 50
    }
  }

  network_interface {
    network    = local.network
    subnetwork = local.subnetwork
    network_ip = "********"
  }

  # Service account configuration
  service_account {
    email  = "<EMAIL>"
    scopes = [
      "https://www.googleapis.com/auth/cloud-platform",
      "https://www.googleapis.com/auth/compute"
    ]
  }

  # Metadata including SSH keys and OS config
  metadata = {
    enable-osconfig = "TRUE"
    ssh-keys = <<-EOT
      lookman_af:ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBLRovU6XEP8PLgCuljCgNCa5Cz2Sl4X7PgHxfUIfDWZ7yrvpNR0MHsZuMFHTG6g1dYPdp4UMQ/b0zKr7u7XUesc= google-ssh {"userName":"<EMAIL>","expireOn":"2025-06-12T14:37:42+0000"}
      lookman_af:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAHx1kkDCYjRW3oZs5XMdlF2OiYV73BEKhH0QXzM7Q3wFV1W/NZhd2tPKXiAuiku4z8tRiBb8+g/JhnHoOmVuNIRfHRPzdOa6D6EbJ+wNDG6QkhgUX9h8KDBOO83RXFLQrtdwngvIabUhWoY/hAnSjGNy5MgSnDaYj3Yuyf1hjvcdTDiurdHmT0C5R5XwQi7KDRm62o2SIiRDN9OXqtFHprZiqPcpZPo0piy+0GxaXGcB7Zd4JR3cjRKUg9c0o8iB53wE8pVTt4p4Ury/HufAtJmpZgabyzXiFTcfdqL8lRiUpt1zHgxXsuGkO1j5LA83BGz3N6G4i2BxFNPLhR5YUWk= google-ssh {"userName":"<EMAIL>","expireOn":"2025-06-12T14:37:45+0000"}
    EOT
  }
}
