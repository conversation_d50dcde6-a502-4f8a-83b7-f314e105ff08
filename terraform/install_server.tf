# Custom Image Resource
resource "google_compute_image" "custom_image" {
  name         = "base-images-lif-prod-2023-ubuntu22-04-25"
  description  = "Custom image created from my-custom-snapshot"
  source_image = "https://www.googleapis.com/compute/v1/projects/lif-prod/global/images/base-images-lif-prod-2023-ubuntu22-04"
}

# Reusable locals
locals {
  zone         = "asia-southeast2-a"
  network      = google_compute_network.lif_vpc_prod.name
  subnetwork   = google_compute_subnetwork.private_subnet.name
  image_link   = google_compute_image.custom_image.self_link
}

# Redis Server
resource "google_compute_instance" "redis_server" {
  name         = "lif-prod-redis"
  machine_type = "e2-custom-2-4096"
  zone         = local.zone
  tags         = ["allow-ssh", "allow-icmp"]
  allow_stopping_for_update = true

  boot_disk {
    initialize_params {
      image = local.image_link
      # WARNING: Changing this will recreate the instance
      size  = 50
    }
  }

  network_interface {
    network    = local.network
    subnetwork = local.subnetwork
    network_ip = "********"
  }
}

# RabbitMQ Server
resource "google_compute_instance" "rabbitmq_server" {
  name         = "lif-prod-rabbitmq"
  machine_type = "e2-custom-2-4096"
  zone         = local.zone
  tags         = ["allow-ssh", "allow-icmp"]
  allow_stopping_for_update = true

  boot_disk {
    initialize_params {
      image = local.image_link
      size  = 20
    }
  }

  network_interface {
    network    = local.network
    subnetwork = local.subnetwork
    network_ip = "********"
  }
}

# MongoDB Server
resource "google_compute_instance" "mongodb_server" {
  name         = "lif-prod-mongodb"
  machine_type = "e2-custom-2-4096"
  zone         = local.zone
  tags         = ["allow-ssh", "allow-icmp"]
  allow_stopping_for_update = true

  boot_disk {
    initialize_params {
      image = local.image_link
      size  = 100
    }
  }

  network_interface {
    network    = local.network
    subnetwork = local.subnetwork
    network_ip = "********"
  }
}

# PostgreSQL Server
resource "google_compute_instance" "postgres_server" {
  name         = "lif-prod-postgres"
  machine_type = "e2-custom-4-4096"
  zone         = local.zone
  tags         = ["allow-ssh", "allow-icmp"]
  allow_stopping_for_update = true

  boot_disk {
    initialize_params {
      image = local.image_link
      size  = 50
    }
  }

  network_interface {
    network    = local.network
    subnetwork = local.subnetwork
    network_ip = "********"
  }
}
