# Project Configuration
variable "project_id" {
  description = "The GCP project ID"
  type        = string
  default     = "lif-prod"
}

variable "region" {
  description = "The GCP region"
  type        = string
  default     = "asia-southeast2"
}

variable "zone" {
  description = "The GCP zone"
  type        = string
  default     = "asia-southeast2-a"
}

# GKE Cluster Configuration
variable "cluster_name" {
  description = "Name of the GKE cluster"
  type        = string
  default     = "lif-prod-cluster-1"
}

variable "cluster_location" {
  description = "Location of the GKE cluster"
  type        = string
  default     = "asia-southeast2-a"
}

variable "release_channel" {
  description = "Release channel for GKE cluster"
  type        = string
  default     = "REGULAR"
  validation {
    condition     = contains(["RAPID", "REGULAR", "STABLE"], var.release_channel)
    error_message = "Release channel must be one of: RAPID, REGULAR, STABLE."
  }
}

variable "initial_node_count" {
  description = "Initial number of nodes in the cluster"
  type        = number
  default     = 0
}

variable "enable_shielded_nodes" {
  description = "Enable shielded nodes"
  type        = bool
  default     = true
}

variable "deletion_protection" {
  description = "Enable deletion protection"
  type        = bool
  default     = true
}

# Network Configuration
variable "network_name" {
  description = "Name of the VPC network"
  type        = string
  default     = "lif-vpc-prod"
}

variable "subnetwork_name" {
  description = "Name of the subnetwork"
  type        = string
  default     = "public-subnet"
}

variable "pods_secondary_range_name" {
  description = "Name of the secondary range for pods"
  type        = string
  default     = "gke-lif-prod-cluster-1-pods-853ee8c2"
}

variable "services_secondary_range_name" {
  description = "Name of the secondary range for services"
  type        = string
  default     = "gke-lif-prod-cluster-1-services-853ee8c2"
}

# Master Authorized Networks
variable "authorized_networks" {
  description = "List of authorized networks for master access"
  type = list(object({
    cidr_block   = string
    display_name = string
  }))
  default = [
    {
      cidr_block   = "********/24"
      display_name = "private"
    },
    {
      cidr_block   = "********/24"
      display_name = "public"
    },
    {
      cidr_block   = "************/32"
      display_name = "lookman"
    },
    {
      cidr_block   = "************/32"
      display_name = "roy"
    },
    {
      cidr_block   = "*************/32"
      display_name = "lookman-fm"
    }
  ]
}

variable "gcp_public_cidrs_access_enabled" {
  description = "Enable access from GCP public CIDRs"
  type        = bool
  default     = true
}



# Logging and Monitoring
variable "logging_components" {
  description = "List of logging components to enable"
  type        = list(string)
  default     = ["SYSTEM_COMPONENTS", "WORKLOADS"]
}

variable "monitoring_components" {
  description = "List of monitoring components to enable"
  type        = list(string)
  default     = ["SYSTEM_COMPONENTS"]
}

# Addons Configuration
variable "enable_gce_persistent_disk_csi_driver" {
  description = "Enable GCE Persistent Disk CSI Driver"
  type        = bool
  default     = true
}

variable "disable_horizontal_pod_autoscaling" {
  description = "Disable horizontal pod autoscaling"
  type        = bool
  default     = false
}

variable "disable_http_load_balancing" {
  description = "Disable HTTP load balancing"
  type        = bool
  default     = false
}



variable "disable_network_policy" {
  description = "Disable network policy"
  type        = bool
  default     = true
}

variable "enable_gcs_fuse_csi_driver" {
  description = "Enable GCS FUSE CSI Driver"
  type        = bool
  default     = false
}

variable "enable_dns_cache" {
  description = "Enable DNS cache"
  type        = bool
  default     = false
}

variable "datapath_provider" {
  description = "Datapath provider for the cluster"
  type        = string
  default     = "LEGACY_DATAPATH"
  validation {
    condition     = contains(["LEGACY_DATAPATH", "ADVANCED_DATAPATH"], var.datapath_provider)
    error_message = "Datapath provider must be either LEGACY_DATAPATH or ADVANCED_DATAPATH."
  }
}
