# Project Configuration
project_id = "lif-prod"
region     = "asia-southeast2"
zone       = "asia-southeast2-a"

# GKE Cluster Configuration
cluster_name     = "lif-prod-cluster-1"
cluster_location = "asia-southeast2-a"
release_channel  = "REGULAR"

# Network Configuration
network_name    = "lif-vpc-prod"
subnetwork_name = "public-subnet"

# Secondary IP ranges for pods and services
pods_secondary_range_name     = "gke-lif-prod-cluster-1-pods-853ee8c2"
services_secondary_range_name = "gke-lif-prod-cluster-1-services-853ee8c2"

# Master Authorized Networks
authorized_networks = [
  {
    cidr_block   = "********/24"
    display_name = "private"
  },
  {
    cidr_block   = "********/24"
    display_name = "public"
  },
  {
    cidr_block   = "************/32"
    display_name = "lookman"
  },
  {
    cidr_block   = "************/32"
    display_name = "roy"
  },
  {
    cidr_block   = "*************/32"
    display_name = "lookman-fm"
  }
]

# Security Settings
enable_shielded_nodes                = true
deletion_protection                  = true
gcp_public_cidrs_access_enabled     = true

# Logging and Monitoring
logging_components    = ["SYSTEM_COMPONENTS", "WORKLOADS"]
monitoring_components = ["SYSTEM_COMPONENTS"]

# Addons Configuration
enable_gce_persistent_disk_csi_driver = true
disable_horizontal_pod_autoscaling    = false
disable_http_load_balancing          = false
disable_network_policy               = true
enable_gcs_fuse_csi_driver          = false
enable_dns_cache                    = false

# Datapath Provider
datapath_provider = "LEGACY_DATAPATH"
