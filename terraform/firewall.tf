
############## creating FIREWALL ###############

# Create ssh firewall 
resource "google_compute_firewall" "allow_ssh" {
  name    = "allow-ssh"
  network = google_compute_network.lif_vpc_prod.name

  allow {
    protocol = "tcp"
    ports    = ["22"]
  }

  source_ranges = ["0.0.0.0/0"]  # Adjust this as needed for security
  target_tags   = ["allow-ssh"]
}


# Create ICMP firewall rule for ping
resource "google_compute_firewall" "allow_icmp" {
  name    = "allow-icmp"
  network = google_compute_network.lif_vpc_prod.name

  allow {
    protocol = "icmp"
  }

  source_ranges = ["********/32"]  # Use the specific IP address of your bastion server
  target_tags   = ["allow-icmp"]
}


# Create ICMP firewall rule for ping
resource "google_compute_firewall" "allow_icmp_icmp" {
  name    = "allow-tcp-2222"
  network = google_compute_network.lif_vpc_prod.name

  allow {
    protocol = "tcp"
  }

  source_ranges = ["0.0.0.0/0"]  # Use the specific IP address of your bastion server
  target_tags   = ["allow-icmp"]
}

# Create Redis firewall rule
# resource "google_compute_firewall" "allow_redis" {
#   name    = "allow-redis"
#   network = google_compute_network.lif_vpc_prod.name

#   allow {
#     protocol = "tcp"
#     ports    = ["6379"]  # Redis default port
#   }

#   source_ranges = ["0.0.0.0/0"]  # Adjust this as needed for security
#   target_tags   = ["allow-redis"]
# }

# Create MongoDB firewall rule
# resource "google_compute_firewall" "allow_mongodb" {
#   name    = "allow-mongodb"
#   network = google_compute_network.lif_vpc_prod.name

#   allow {
#     protocol = "tcp"
#     ports    = ["27017"]  # MongoDB default port
#   }

#   source_ranges = ["0.0.0.0/0"]  # Adjust this as needed for security
#   target_tags   = ["allow-mongodb"]
# }

# Create RabbitMQ firewall rule
# resource "google_compute_firewall" "allow_rabbitmq" {
#   name    = "allow-rabbitmq"
#   network = google_compute_network.lif_vpc_prod.name

#   allow {
#     protocol = "tcp"
#     ports    = ["5672"]  # RabbitMQ default port
#   }

#   source_ranges = ["0.0.0.0/0"]  # Adjust this as needed for security
#   target_tags   = ["allow-rabbitmq"]
# }

# Create PostgreSQL firewall rule
# resource "google_compute_firewall" "allow_postgresql" {
#   name    = "allow-postgresql"
#   network = google_compute_network.lif_vpc_prod.name

#   allow {
#     protocol = "tcp"
#     ports    = ["5432"]  # PostgreSQL default port
#   }

#   source_ranges = ["0.0.0.0/0"]  # Adjust this as needed for security
#   target_tags   = ["allow-postgresql"]
# }
